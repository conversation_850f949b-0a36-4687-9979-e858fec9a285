<?php
/**
 * Test script to manually verify pickup location functionality
 * Add this to your WordPress site temporarily to test
 * 
 * Usage: Visit yoursite.com/wp-admin/admin.php?page=test-pickup-functionality
 */

// Only run for admins
if (!current_user_can('manage_options')) {
    wp_die('Access denied');
}

// Add admin menu
add_action('admin_menu', function() {
    add_submenu_page(
        'woocommerce',
        'Test Pickup Functionality',
        'Test Pickup',
        'manage_options',
        'test-pickup-functionality',
        'render_pickup_test_page'
    );
});

function render_pickup_test_page() {
    echo '<div class="wrap">';
    echo '<h1>🧪 Pickup Location Functionality Test</h1>';
    
    // Test 1: Check if plugin class exists
    echo '<h2>Test 1: Plugin Status</h2>';
    if (class_exists('WC_Pickup_Locations')) {
        echo '<p>✅ WC_Pickup_Locations class exists</p>';
        
        // Check if methods exist
        $methods_to_check = array(
            'replace_shipping_address_with_pickup',
            'modify_checkout_posted_data',
            'apply_pickup_address_to_order',
            'should_process_pickup_replacement'
        );
        
        foreach ($methods_to_check as $method) {
            if (method_exists('WC_Pickup_Locations', $method)) {
                echo '<p>✅ Method ' . $method . ' exists</p>';
            } else {
                echo '<p>❌ Method ' . $method . ' missing</p>';
            }
        }
    } else {
        echo '<p>❌ WC_Pickup_Locations class not found</p>';
    }
    
    // Test 2: Check hooks registration
    echo '<h2>Test 2: Hooks Registration</h2>';
    $hooks_to_check = array(
        'woocommerce_checkout_posted_data' => 'modify_checkout_posted_data',
        'woocommerce_checkout_create_order' => 'replace_shipping_on_order_create',
        'woocommerce_checkout_update_order_meta' => 'replace_shipping_address_with_pickup',
        'woocommerce_new_order' => 'replace_shipping_address_new_order',
        'woocommerce_thankyou' => 'replace_shipping_address_thankyou'
    );
    
    foreach ($hooks_to_check as $hook => $callback) {
        $priority = has_action($hook, array('WC_Pickup_Locations', $callback));
        if ($priority !== false) {
            echo '<p>✅ Hook ' . $hook . ' → ' . $callback . ' (Priority: ' . $priority . ')</p>';
        } else {
            echo '<p>❌ Hook ' . $hook . ' → ' . $callback . ' not registered</p>';
        }
    }
    
    // Test 3: Check recent orders
    echo '<h2>Test 3: Recent Orders Analysis</h2>';
    $recent_orders = wc_get_orders(array(
        'limit' => 10,
        'orderby' => 'date',
        'order' => 'DESC',
    ));
    
    echo '<table class="wp-list-table widefat fixed striped">';
    echo '<thead><tr><th>Order ID</th><th>Pickup Location</th><th>Shipping Address</th><th>Status</th></tr></thead>';
    echo '<tbody>';
    
    foreach ($recent_orders as $order) {
        $pickup_location = get_post_meta($order->get_id(), '_pickup_location', true);
        $shipping_address = $order->get_shipping_address_1();
        
        echo '<tr>';
        echo '<td>#' . $order->get_id() . '</td>';
        echo '<td>' . ($pickup_location ? '✅ ' . esc_html(substr($pickup_location, 0, 30)) . '...' : '❌ None') . '</td>';
        echo '<td>' . ($shipping_address ? esc_html(substr($shipping_address, 0, 40)) . '...' : 'None') . '</td>';
        echo '<td>' . $order->get_status() . '</td>';
        echo '</tr>';
    }
    
    echo '</tbody></table>';
    
    // Test 4: Manual test form
    echo '<h2>Test 4: Manual Address Replacement Test</h2>';
    
    if (isset($_POST['test_replacement'])) {
        $test_pickup_location = sanitize_text_field($_POST['test_pickup_location']);
        $test_order_id = intval($_POST['test_order_id']);
        
        if ($test_order_id && $test_pickup_location) {
            $order = wc_get_order($test_order_id);
            if ($order) {
                // Create instance of our plugin class
                $plugin_instance = new WC_Pickup_Locations();
                
                // Use reflection to call private method
                $reflection = new ReflectionClass($plugin_instance);
                $method = $reflection->getMethod('apply_pickup_address_to_order');
                $method->setAccessible(true);
                
                $result = $method->invoke($plugin_instance, $order, $test_pickup_location);
                
                if ($result) {
                    echo '<div class="notice notice-success"><p>✅ Successfully applied pickup address to order #' . $test_order_id . '</p></div>';
                } else {
                    echo '<div class="notice notice-error"><p>❌ Failed to apply pickup address to order #' . $test_order_id . '</p></div>';
                }
            } else {
                echo '<div class="notice notice-error"><p>❌ Order #' . $test_order_id . ' not found</p></div>';
            }
        }
    }
    
    echo '<form method="post">';
    echo '<table class="form-table">';
    echo '<tr>';
    echo '<th><label for="test_order_id">Order ID to Test:</label></th>';
    echo '<td><input type="number" id="test_order_id" name="test_order_id" placeholder="Enter order ID" /></td>';
    echo '</tr>';
    echo '<tr>';
    echo '<th><label for="test_pickup_location">Test Pickup Location:</label></th>';
    echo '<td><input type="text" id="test_pickup_location" name="test_pickup_location" placeholder="ID	Name	Address" style="width:400px;" /></td>';
    echo '</tr>';
    echo '</table>';
    echo '<p class="submit"><input type="submit" name="test_replacement" class="button-primary" value="Test Address Replacement" /></p>';
    echo '</form>';
    
    echo '<p><strong>Sample pickup location format:</strong><br>';
    echo '<code>SF001	順豐自取點 - 中環店	香港中環德輔道中123號</code></p>';
    
    // Test 5: JavaScript test
    echo '<h2>Test 5: JavaScript Functionality</h2>';
    echo '<p>Check browser console for JavaScript logs when testing on checkout page.</p>';
    
    echo '<script>
    console.log("=== PICKUP LOCATION TEST SCRIPT LOADED ===");
    if (typeof pickupLocations !== "undefined") {
        console.log("✅ pickupLocations data available:", pickupLocations);
    } else {
        console.log("❌ pickupLocations data not available");
    }
    </script>';
    
    echo '</div>';
}
?>
