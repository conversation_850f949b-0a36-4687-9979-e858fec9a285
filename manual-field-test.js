// Manual test script - paste this in browser console on checkout page
// This will help us manually test and find the shipping fields

console.log('=== MANUAL SHIPPING FIELD TEST ===');

// Test data
var testPickupName = '上環文樂商廈順豐站';
var testPickupAddress = '香港香港島中西區中上環文咸東街91號及永樂街117號文樂商業大廈地下A號舖';

// Step 1: Check if "Ship to different address" checkbox exists and check it
function enableShippingFields() {
    console.log('Step 1: Enabling shipping fields...');
    
    var checkboxSelectors = [
        '#ship-to-different-address-checkbox',
        'input[name="ship_to_different_address"]',
        '#ship_to_different_address',
        'input[value="1"][name*="ship"]'
    ];
    
    var checkbox = null;
    for (var i = 0; i < checkboxSelectors.length; i++) {
        var cb = document.querySelector(checkboxSelectors[i]);
        if (cb) {
            checkbox = cb;
            console.log('Found checkbox with selector:', checkboxSelectors[i]);
            break;
        }
    }
    
    if (checkbox) {
        if (!checkbox.checked) {
            checkbox.checked = true;
            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
            console.log('✅ Checked "Ship to different address"');
            return true;
        } else {
            console.log('✅ "Ship to different address" already checked');
            return true;
        }
    } else {
        console.log('❌ Could not find "Ship to different address" checkbox');
        return false;
    }
}

// Step 2: Find all shipping fields
function findAllShippingFields() {
    console.log('Step 2: Finding all shipping fields...');
    
    var fields = {};
    var fieldNames = ['first_name', 'last_name', 'company', 'address_1', 'address_2', 'city', 'state', 'postcode', 'country'];
    
    fieldNames.forEach(function(fieldName) {
        var selectors = [
            '#shipping_' + fieldName,
            'input[name="shipping_' + fieldName + '"]',
            'select[name="shipping_' + fieldName + '"]',
            '.woocommerce-shipping-fields input[name="shipping_' + fieldName + '"]',
            '.woocommerce-shipping-fields select[name="shipping_' + fieldName + '"]'
        ];
        
        var field = null;
        for (var i = 0; i < selectors.length; i++) {
            var f = document.querySelector(selectors[i]);
            if (f) {
                field = f;
                console.log('Found ' + fieldName + ' with selector:', selectors[i]);
                break;
            }
        }
        
        if (!field) {
            // Fallback search
            var allInputs = document.querySelectorAll('input, select');
            for (var j = 0; j < allInputs.length; j++) {
                var input = allInputs[j];
                var name = input.name || '';
                var id = input.id || '';
                if ((name.includes('shipping') && name.includes(fieldName)) || 
                    (id.includes('shipping') && id.includes(fieldName))) {
                    field = input;
                    console.log('Found ' + fieldName + ' with fallback search:', name || id);
                    break;
                }
            }
        }
        
        if (field) {
            fields[fieldName] = field;
        } else {
            console.log('❌ Could not find field:', fieldName);
        }
    });
    
    return fields;
}

// Step 3: Update the fields
function updateFields(fields) {
    console.log('Step 3: Updating fields...');
    
    var updates = {
        first_name: testPickupName,
        last_name: '',
        company: testPickupName,
        address_1: testPickupAddress,
        address_2: '',
        city: '香港島',
        state: '',
        postcode: '',
        country: 'HK'
    };
    
    var updatedCount = 0;
    
    Object.keys(updates).forEach(function(fieldName) {
        if (fields[fieldName]) {
            var field = fields[fieldName];
            var value = updates[fieldName];
            
            console.log('Updating ' + fieldName + ' to:', value);
            
            field.value = value;
            field.dispatchEvent(new Event('input', { bubbles: true }));
            field.dispatchEvent(new Event('change', { bubbles: true }));
            field.dispatchEvent(new Event('blur', { bubbles: true }));
            
            // Visual feedback
            field.style.backgroundColor = '#e8f5e8';
            field.style.borderColor = '#4CAF50';
            
            updatedCount++;
        }
    });
    
    console.log('Updated ' + updatedCount + ' fields');
    return updatedCount;
}

// Step 4: Run the complete test
function runCompleteTest() {
    console.log('=== RUNNING COMPLETE TEST ===');
    
    // Enable shipping fields
    var checkboxEnabled = enableShippingFields();
    
    // Wait a moment for fields to appear
    setTimeout(function() {
        // Find fields
        var fields = findAllShippingFields();
        var fieldCount = Object.keys(fields).length;
        
        console.log('Found ' + fieldCount + ' shipping fields');
        
        if (fieldCount > 0) {
            // Update fields
            var updatedCount = updateFields(fields);
            
            if (updatedCount > 0) {
                console.log('✅ SUCCESS: Updated ' + updatedCount + ' shipping fields');
                console.log('Check the checkout form - shipping fields should now show pickup location');
            } else {
                console.log('❌ FAILED: Could not update any fields');
            }
        } else {
            console.log('❌ FAILED: No shipping fields found');
            
            // List all form fields for debugging
            console.log('All form fields on page:');
            var allInputs = document.querySelectorAll('input, select');
            allInputs.forEach(function(input, index) {
                var name = input.name || '';
                var id = input.id || '';
                if (name.includes('shipping') || id.includes('shipping') || name || id) {
                    console.log(index + ': ' + input.tagName + ' name="' + name + '" id="' + id + '"');
                }
            });
        }
    }, checkboxEnabled ? 1000 : 100);
}

// Run the test
runCompleteTest();

// Also make the function available globally for manual testing
window.testShippingFieldUpdate = runCompleteTest;

console.log('=== TEST COMPLETE ===');
console.log('You can run testShippingFieldUpdate() again to repeat the test');
